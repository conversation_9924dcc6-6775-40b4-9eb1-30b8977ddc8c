"""
A shared memory interface to enrich context across steps.
- Feeds data into `state.context`, including:
  -> Historical task traces.
  -> RAG-retrieved relevant information.
  -> Agent/system memory.
- Used by planner and agents to reason and personalize behavior.
"""

from __future__ import annotations

import uuid  # For generating unique task IDs
from enum import Enum
from typing import Optional, List, Dict, Any, Union, Set # Type hints for optional values (Optional), multiple types (Union), and unique collections (Set)
from datetime import datetime, timezone

from pydantic import BaseModel, Field
from .state import OrchestrationStep, LangGraphState

import logging
logger = logging.getLogger(__name__)


class MemoryUnits(BaseModel):
    """Defines what encompasses memory. 
    I.e, models the unit of memory - a single event, reflection, observation or message
    considered worth remembering during orchestration."""
    memory_id: uuid.UUID = Field(default_factory=uuid.uuid4, description="unique identifier for each memory record")
    timestamp: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    current_step: OrchestrationStep = Field(default_factory=OrchestrationStep, description="step in the orchestration process when this memory was generated")
    content: str = Field(default_factory=str, description="content of the event being recorded")
    agent: str = Field(default_factory=str, description="agent that generated this memory")
    task_id: str = Field(default_factory=str, description="task ID that generated this memory")
    metadata: Dict[str, Any] = Field(default_factory=dict)
    tags: List[str] = Field(default_factory=list, description="optional tags for filtering/searching memory")


class GlobalMemory:
    def __init__(self, first_memory: Optional[List[MemoryUnits]] = None):
        self.memory: List[MemoryUnits] = first_memory or []
        

    def add_memory(self, memory_unit: MemoryUnits):
        for existing in self.memory:
            if existing.memory_id == memory_unit.memory_id:
                return memory_unit.memory_id

        self.memory.append(memory_unit)
        logger.debug(f"[Memory] Added memory: {memory_unit.memory_id}, step={memory_unit.current_step}, agent={memory_unit.agent}, task={memory_unit.task_id}")
        return memory_unit.memory_id
    

    def get_relevant_memory(self, filters: Dict) -> List[MemoryUnits]:
        """
        Retrieve memory units that match the given filters.

        Args:
            filters: Dictionary with filter criteria (agent, task_id, tags, etc.)

        Returns:
            List of MemoryUnits that match the filters
        """
        if not self.memory:
            return []

        relevant_memories = []

        for memory_unit in self.memory:
            # Check if memory matches all filter criteria
            matches = True

            # Filter by agent
            if "agent" in filters and memory_unit.agent != filters["agent"]:
                matches = False

            # Filter by task_id
            if "task_id" in filters and memory_unit.task_id != filters["task_id"]:
                matches = False

            # Filter by current_step
            if "current_step" in filters and memory_unit.current_step != filters["current_step"]:
                matches = False

            # Filter by tags (memory must have ALL specified tags)
            if "tags" in filters:
                required_tags = filters["tags"] if isinstance(filters["tags"], list) else [filters["tags"]]
                if not all(tag in memory_unit.tags for tag in required_tags):
                    matches = False

            # Filter by any tag (memory must have AT LEAST ONE of the specified tags)
            if "any_tags" in filters:
                any_tags = filters["any_tags"] if isinstance(filters["any_tags"], list) else [filters["any_tags"]]
                if not any(tag in memory_unit.tags for tag in any_tags):
                    matches = False

            if matches:
                relevant_memories.append(memory_unit)

        # Sort by timestamp (most recent first)
        relevant_memories.sort(key=lambda x: x.timestamp, reverse=True)

        # Limit results if specified
        limit = filters.get("limit", None)
        if limit and isinstance(limit, int):
            relevant_memories = relevant_memories[:limit]

        logger.debug(f"[Memory] Retrieved {len(relevant_memories)} memories matching filters: {filters}")
        return relevant_memories
    

    def inject_into_context(self, state: LangGraphState) -> LangGraphState:
        """
        Inject relevant memory into the LangGraphState context.

        Args:
            state: Current LangGraphState

        Returns:
            Updated LangGraphState with memory context injected
        """
        # Get recent memories for context
        recent_memories = self.get_relevant_memory({
            "limit": 10,
            "any_tags": ["plan_step", "agent_assignment", "strategic", "llm_output"]
        })

        # Format memories for context
        memory_context = []
        for memory in recent_memories:
            memory_context.append({
                "timestamp": memory.timestamp.isoformat(),
                "agent": memory.agent,
                "task_id": memory.task_id,
                "content": memory.content[:200] + "..." if len(memory.content) > 200 else memory.content,
                "tags": memory.tags
            })

        # Inject into state context
        if "memory_context" not in state.context:
            state.context["memory_context"] = []

        state.context["memory_context"] = memory_context

        logger.debug(f"[Memory] Injected {len(memory_context)} memories into state context")
        return state