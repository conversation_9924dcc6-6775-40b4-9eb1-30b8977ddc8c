"""🧠 State in LangGraph:
State is a mutable object (usually a pydantic.BaseModel) that gets passed between nodes in the graph. 
It acts as shared memory and stores all relevant information (e.g., user input, task progress, agent outputs, etc.) - 
updated at each node to reflect the system's evolving understanding and decisions.

This script defines the following classes: 
- LangGraphState(BaseModel)
- Task(BaseModel)
- TaskType
- TaskStatus
- OrchestrationSteps
"""

from __future__ import annotations

import uuid
from enum import Enum
from typing import Optional, List, Dict, Any, Union, Set # Type hints for optional values (Optional), multiple types (Union), and unique collections (Set)

from pydantic import BaseModel, ConfigDict, Field, model_validator
from .tracer import TraceEvent

class OrchestrationStep(str, Enum):
    """Defines the major execution phases (i.e., nodes) in LangGraph-based multi-agent orchestration."""
    PLANNING = "PLANNING"  # Initial user query → Task list generation
    TASK_ASSIGNMENT = "TASK_ASSIGNMENT"  # Assign agents to task
    TASK_EXECUTION = "TASK_EXECUTION"  # Run agent(s) on a task
    REVIEW = "REVIEW"  # Sanity check for potential improvement iteration´
    DONE = "DONE" # Final orchestration state


class TaskType(str, Enum):
    """Describes the intent or behavior category of a task assigned to an agent."""
    QUERY_DECOMPOSITION = "QUERY_DECOMPOSITION"
    REASONING = "REASONING"
    COMMUNICATION = "COMMUNICATION"
    TOOLS = "TOOLS"
    ACTION = "ACTION"
    EVALUATION = "EVALUATION"


class TaskStatus(str, Enum):
    """Tracks the progress of individual tasks"""
    PENDING = "PENDING"
    IN_PROGRESS = "IN_PROGRESS"
    COMPLETED = "COMPLETED"
    FAILED = "FAILED"


class Task(BaseModel):
    """Defines a unit of work (i.e., task) for an agent to perform"""
    task_id: str = Field(default_factory=lambda: f"task_{uuid.uuid4().hex[:8]}")
    description: str = Field(default="")
    task_type: TaskType = Field(default=TaskType.REASONING)
    status: TaskStatus = Field(default=TaskStatus.PENDING)
    dependencies: Set[str] = Field(default_factory=set)  # IDs of tasks that must finish before this task can run; set ensures unique IDs
    assigned_agent: Optional[str] = None
    output: Optional[Any] = None
    metadata: Dict[str, Any] = Field(default_factory=dict)  # Additional metadata for task enrichment and tracking

    model_config = ConfigDict(use_enum_values=True)  # Serialize enums as values (e.g., "PENDING") instead of Enum objects (e.g., TaskStatus.PENDING) for non-pydantic use-cases


class LangGraphState(BaseModel):
    """State object passed between LangGraph nodes to coordinate multi-agent tasks and context."""
    user_input: str
    context: Dict[str, Any] = Field(default_factory=dict)  # Shared state across tasks; default_factory uses fresh dict per instance to avoid mutation bugs 
    coordinator_plan_id: Optional[str] = None

    tasks: List[Task]
    current_task_id: Optional[str] = None
    current_step: OrchestrationStep

    trace: List[Union[TraceEvent, dict, str]] = Field(default_factory=list)

    agent_outputs: Dict[str, Any] = Field(default_factory=dict) 
    tool_outputs: Dict[str, Any] = Field(default_factory=dict)  

    model_config = ConfigDict(
        extra='allow',  # Accept and store fields not explicitly defined in LangGraphState
        arbitrary_types_allowed=True,  # Allow non-standard types (e.g., custom classes like TraceEvent)
        frozen=False,  # Controls mutability; False = mutable (i.e., allow state updates during execution)
        use_enum_values=True  # Serialize enums as values (e.g., "PENDING") instead of Enum objects (e.g., TaskStatus.PENDING) for non-pydantic use-cases
    )

    def get_task_by_id(self, task_id: str) -> Optional[Task]:
        """Get a task by its ID."""
        for task in self.tasks:
            if task.task_id == task_id:
                return task
        return None

    def is_terminal(self) -> bool:
        """Check if the orchestration has reached its end."""
        return self.current_step == OrchestrationStep.DONE

    def get_pending_tasks(self) -> List[Task]:
        """Get all tasks with PENDING status."""
        return [task for task in self.tasks if task.status == TaskStatus.PENDING]

    def get_completed_tasks(self) -> List[Task]:
        """Get all tasks with COMPLETED status."""
        return [task for task in self.tasks if task.status == TaskStatus.COMPLETED]

    @model_validator(mode="after")
    def check_current_task_id(self) -> LangGraphState:
        """Ensure the current_task_id, if set, refers to an existing task."""
        if self.current_task_id and not any(task.task_id == self.current_task_id for task in self.tasks):
            raise ValueError(f"Invalid current_task_id: {self.current_task_id} not found in tasks")
        return self