"""
Timeout and Retry Utilities

This module provides timeout and retry decorators for robust operation handling.
"""

import asyncio
import logging
import functools
from typing import Any, Callable, Optional, Union, Dict
from ..errors import TimeoutError

logger = logging.getLogger(__name__)


async def with_timeout_and_retry(
    func: Callable,
    *args,
    timeout_seconds: float = 30,
    operation_name: str = "operation",
    max_attempts: int = 3,
    **kwargs
) -> Any:
    """
    Execute an async function with timeout and retry logic.
    
    Args:
        func: The async function to execute
        *args: Positional arguments for the function
        timeout_seconds: Timeout in seconds
        operation_name: Name of the operation for logging
        max_attempts: Maximum number of attempts
        **kwargs: Keyword arguments for the function
        
    Returns:
        Result of the function execution
        
    Raises:
        TimeoutError: If all attempts timeout
        Exception: If function raises an exception after all retries
    """
    last_exception = None
    
    for attempt in range(max_attempts):
        try:
            logger.debug(f"Attempting {operation_name} (attempt {attempt + 1}/{max_attempts})")
            
            # Execute with timeout
            result = await asyncio.wait_for(
                func(*args, **kwargs),
                timeout=timeout_seconds
            )
            
            logger.debug(f"Successfully completed {operation_name}")
            return result
            
        except asyncio.TimeoutError as e:
            last_exception = e
            logger.warning(
                f"{operation_name} timed out after {timeout_seconds}s "
                f"(attempt {attempt + 1}/{max_attempts})"
            )
            
        except Exception as e:
            last_exception = e
            logger.warning(
                f"{operation_name} failed with {type(e).__name__}: {e} "
                f"(attempt {attempt + 1}/{max_attempts})"
            )
            
        # Wait before retry (exponential backoff)
        if attempt < max_attempts - 1:
            wait_time = 2 ** attempt  # 1s, 2s, 4s, etc.
            logger.debug(f"Waiting {wait_time}s before retry...")
            await asyncio.sleep(wait_time)
    
    # All attempts failed
    if isinstance(last_exception, asyncio.TimeoutError):
        raise TimeoutError(
            f"{operation_name} failed after {max_attempts} attempts (timeout: {timeout_seconds}s)",
            context={"operation": operation_name, "timeout": timeout_seconds, "attempts": max_attempts}
        )
    else:
        logger.error(f"{operation_name} failed after {max_attempts} attempts: {last_exception}")
        raise last_exception


def with_timeout_decorator(
    timeout_seconds: float = 30,
    operation_name: Optional[str] = None,
    max_attempts: int = 1
):
    """
    Decorator for adding timeout to async functions.
    
    Args:
        timeout_seconds: Timeout in seconds
        operation_name: Name of the operation for logging
        max_attempts: Maximum number of attempts
        
    Returns:
        Decorated function
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            op_name = operation_name or f"{func.__module__}.{func.__name__}"
            return await with_timeout_and_retry(
                func,
                *args,
                timeout_seconds=timeout_seconds,
                operation_name=op_name,
                max_attempts=max_attempts,
                **kwargs
            )
        return wrapper
    return decorator


def with_retry_decorator(
    max_attempts: int = 3,
    operation_name: Optional[str] = None,
    backoff_factor: float = 1.0
):
    """
    Decorator for adding retry logic to async functions.
    
    Args:
        max_attempts: Maximum number of attempts
        operation_name: Name of the operation for logging
        backoff_factor: Multiplier for exponential backoff
        
    Returns:
        Decorated function
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            op_name = operation_name or f"{func.__module__}.{func.__name__}"
            last_exception = None
            
            for attempt in range(max_attempts):
                try:
                    logger.debug(f"Attempting {op_name} (attempt {attempt + 1}/{max_attempts})")
                    result = await func(*args, **kwargs)
                    logger.debug(f"Successfully completed {op_name}")
                    return result
                    
                except Exception as e:
                    last_exception = e
                    logger.warning(
                        f"{op_name} failed with {type(e).__name__}: {e} "
                        f"(attempt {attempt + 1}/{max_attempts})"
                    )
                    
                    # Wait before retry
                    if attempt < max_attempts - 1:
                        wait_time = backoff_factor * (2 ** attempt)
                        logger.debug(f"Waiting {wait_time}s before retry...")
                        await asyncio.sleep(wait_time)
            
            # All attempts failed
            logger.error(f"{op_name} failed after {max_attempts} attempts: {last_exception}")
            raise last_exception
            
        return wrapper
    return decorator
