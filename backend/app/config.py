"""
Configuration Management

This module provides centralized configuration management using Pydantic BaseSettings.
It handles environment variable loading, validation, and provides default values.
"""

import os
from functools import lru_cache
from typing import Optional, List
from pydantic import Field, validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """
    Application settings loaded from environment variables.

    Uses Pydantic BaseSettings for automatic environment variable loading,
    type validation, and default value management.
    """

    # Core Application Settings
    DEBUG: bool = Field(default=False, description="Enable debug mode")
    DEVELOPMENT_MODE: bool = Field(default=False, description="Enable development mode")
    LOG_LEVEL: str = Field(default="INFO", description="Logging level")
    PORT: int = Field(default=8000, description="Server port")

    # Database Settings
    DATABASE_URL: str = Field(
        default="postgresql://postgres:postgres@localhost:5432/businesslm",
        description="PostgreSQL database URL"
    )
    DATABASE_POOL_SIZE: int = Field(default=5, description="Database connection pool size")
    DATABASE_MAX_OVERFLOW: int = Field(default=10, description="Database max overflow connections")
    DATABASE_POOL_TIMEOUT: int = Field(default=30, description="Database pool timeout in seconds")
    DATABASE_POOL_RECYCLE: int = Field(default=1800, description="Database pool recycle time in seconds")

    # LLM API Keys
    OPENAI_API_KEY: Optional[str] = Field(default=None, description="OpenAI API key")
    ANTHROPIC_API_KEY: Optional[str] = Field(default=None, description="Anthropic API key")
    GOOGLE_API_KEY: Optional[str] = Field(default=None, description="Google API key")
    HUGGINGFACE_API_KEY: Optional[str] = Field(default=None, description="HuggingFace API key")

    # LangSmith Settings (Optional)
    LANGCHAIN_TRACING_V2: bool = Field(default=False, description="Enable LangSmith tracing")
    LANGCHAIN_API_KEY: Optional[str] = Field(default=None, description="LangSmith API key")
    LANGCHAIN_PROJECT: str = Field(default="businesslm-poc", description="LangSmith project name")

    # RAG Settings
    DEFAULT_EMBEDDING_MODEL: str = Field(default="huggingface", description="Default embedding provider")
    EMBEDDING_MODEL: str = Field(default="intfloat/e5-base-v2", description="Embedding model name")
    VECTOR_STORE_TYPE: str = Field(default="pgvector", description="Vector store type")
    VECTOR_STORE_PATH: str = Field(default="./data/vector_store", description="Vector store file path (for file-based stores)")
    VECTOR_DIMENSION: int = Field(default=768, description="Vector embedding dimension")

    # Timeout Settings (in seconds)
    TIMEOUT_EMBEDDING: int = Field(default=30, description="Embedding timeout")
    TIMEOUT_VECTOR_SEARCH: int = Field(default=10, description="Vector search timeout")
    TIMEOUT_KEYWORD_SEARCH: int = Field(default=8, description="Keyword search timeout")
    TIMEOUT_HYBRID_SEARCH: int = Field(default=12, description="Hybrid search timeout")
    TIMEOUT_LLM_DEFAULT: int = Field(default=60, description="Default LLM timeout")

    # LLM Default Settings
    LLM_DEFAULT_TEMPERATURE: float = Field(default=0.7, description="Default LLM temperature")
    LLM_DEFAULT_MAX_TOKENS: int = Field(default=1000, description="Default LLM max tokens")
    LLM_DEFAULT_MAX_RETRIES: int = Field(default=3, description="Default LLM max retries")

    @validator("LOG_LEVEL")
    def validate_log_level(cls, v):
        """Validate log level is one of the allowed values."""
        allowed_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in allowed_levels:
            raise ValueError(f"LOG_LEVEL must be one of {allowed_levels}")
        return v.upper()

    @validator("DEFAULT_EMBEDDING_MODEL")
    def validate_embedding_provider(cls, v):
        """Validate embedding provider is supported."""
        allowed_providers = ["huggingface", "openai"]
        if v.lower() not in allowed_providers:
            raise ValueError(f"DEFAULT_EMBEDDING_MODEL must be one of {allowed_providers}")
        return v.lower()

    @validator("VECTOR_STORE_TYPE")
    def validate_vector_store_type(cls, v):
        """Validate vector store type is supported."""
        allowed_types = ["pgvector", "memory"]  # memory for testing only
        if v.lower() not in allowed_types:
            raise ValueError(f"VECTOR_STORE_TYPE must be one of {allowed_types}")
        return v.lower()

    def validate_api_keys(self) -> List[str]:
        """
        Validate that at least one LLM API key is provided.

        Returns:
            List of missing required configurations
        """
        errors = []

        # Check if at least one LLM API key is provided
        llm_keys = [self.OPENAI_API_KEY, self.ANTHROPIC_API_KEY, self.GOOGLE_API_KEY]
        if not any(llm_keys):
            errors.append("At least one LLM API key must be provided (OPENAI_API_KEY, ANTHROPIC_API_KEY, or GOOGLE_API_KEY)")

        # Check database URL format
        if not self.DATABASE_URL.startswith("postgresql://"):
            errors.append("DATABASE_URL must be a valid PostgreSQL connection string")

        return errors

    class Config:
        """Pydantic configuration."""
        env_file = "../.env"  # Look for .env in project root
        env_file_encoding = "utf-8"
        case_sensitive = True
        extra = "ignore"  # Ignore extra fields (frontend variables, etc.)


@lru_cache()
def get_settings() -> Settings:
    """
    Get application settings with caching.

    Uses lru_cache to ensure settings are loaded only once and cached
    for subsequent calls, improving performance.

    Returns:
        Settings instance with loaded configuration
    """
    settings = Settings()

    # Validate configuration in development mode
    if settings.DEVELOPMENT_MODE:
        validation_errors = settings.validate_api_keys()
        if validation_errors:
            import warnings
            for error in validation_errors:
                warnings.warn(f"Configuration warning: {error}")

    return settings


# Global settings instance for easy import
settings = get_settings()
