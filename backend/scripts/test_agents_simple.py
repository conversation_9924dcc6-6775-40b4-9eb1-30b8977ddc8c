#!/usr/bin/env python3
"""
Simple test script for agents implementation.

This script validates that the agents can be imported and basic functionality works
without requiring the full system dependencies.
"""

import sys
import asyncio
import logging
from pathlib import Path

# Add project root to path for imports
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_basic_agents():
    """Test basic agents functionality."""
    print("🧪 Testing Basic Agents Implementation")
    print("=" * 50)
    
    try:
        # Test basic imports
        print("📦 Testing basic imports...")
        from backend.orchestration.state import Task, TaskType, LangGraphState, OrchestrationStep
        from backend.orchestration.memory import GlobalMemory, MemoryUnits
        from backend.llm import get_llm_adapter
        print("✅ Basic imports successful")
        
        # Test LLM adapter creation
        print("\n🤖 Testing LLM adapter creation...")
        llm_adapter = get_llm_adapter("mock", fallback=True)
        print(f"✅ LLM adapter created: {type(llm_adapter).__name__}")
        
        # Test memory creation
        print("\n🧠 Testing memory creation...")
        memory = GlobalMemory()
        print("✅ Memory created successfully")
        
        # Test task creation
        print("\n📋 Testing task creation...")
        test_task = Task(
            description="Test task for agent validation",
            task_type=TaskType.REASONING,
            assigned_agent="TestAgent"
        )
        print(f"✅ Task created: {test_task.task_id}")
        print(f"   - Description: {test_task.description}")
        print(f"   - Type: {test_task.task_type}")
        print(f"   - Metadata: {test_task.metadata}")
        
        # Test state creation
        print("\n🔄 Testing state creation...")
        test_state = LangGraphState(
            user_input="Test user input for validation",
            tasks=[test_task],
            current_step=OrchestrationStep.PLANNING
        )
        print("✅ LangGraphState created successfully")
        print(f"   - User input: {test_state.user_input[:50]}...")
        print(f"   - Current step: {test_state.current_step}")
        print(f"   - Tasks count: {len(test_state.tasks)}")
        
        # Test memory operations
        print("\n🧠 Testing memory operations...")
        test_memory = MemoryUnits(
            content="Test memory content",
            agent="TestAgent",
            task_id=test_task.task_id,
            current_step=OrchestrationStep.PLANNING,
            tags=["test", "validation"]
        )
        memory.add_memory(test_memory)
        print("✅ Memory added successfully")
        
        # Test memory retrieval
        retrieved_memories = memory.get_relevant_memory({
            "agent": "TestAgent",
            "tags": ["test"]
        })
        print(f"✅ Memory retrieval: {len(retrieved_memories)} memories found")
        
        # Test state utilities
        print("\n🔧 Testing state utilities...")
        found_task = test_state.get_task_by_id(test_task.task_id)
        print(f"✅ Task lookup: {'Found' if found_task else 'Not found'}")
        
        pending_tasks = test_state.get_pending_tasks()
        print(f"✅ Pending tasks: {len(pending_tasks)}")
        
        is_terminal = test_state.is_terminal()
        print(f"✅ Terminal state: {is_terminal}")
        
        # Now test agents if possible
        print("\n👑 Testing agent imports...")
        try:
            from backend.agents.base import BaseAgent
            print("✅ BaseAgent imported successfully")
            
            # Test BaseAgent instantiation
            base_agent = BaseAgent(llm_adapter=llm_adapter, memory=memory)
            print("✅ BaseAgent instantiated successfully")
            
            # Test BaseAgent run method with mock
            print("   - Testing BaseAgent.run() method...")
            try:
                result = await base_agent.run(test_task, test_state)
                print(f"✅ BaseAgent.run() completed: {len(result)} characters returned")
            except Exception as e:
                print(f"⚠️ BaseAgent.run() failed (expected with mock): {e}")
            
        except Exception as e:
            print(f"⚠️ Agent import failed: {e}")
            print("   This is expected if there are dependency issues")
        
        print("\n🎉 Basic tests completed successfully!")
        print("✅ Core orchestration components are working correctly")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    try:
        success = asyncio.run(test_basic_agents())
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
