#!/usr/bin/env python3
"""
Test script for agents implementation.

This script validates that the agents are properly implemented and can be instantiated
without errors. It's a basic smoke test to ensure the implementation is sound.
"""

import sys
import asyncio
import logging
from pathlib import Path

# Add project root to path for imports
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_agents_implementation():
    """Test the agents implementation."""
    print("🧪 Testing Agents Implementation")
    print("=" * 50)
    
    try:
        # Test imports
        print("📦 Testing imports...")
        from backend.agents import BaseAgent, CoCEOAgent
        from backend.llm import get_llm_adapter
        from backend.orchestration.memory import GlobalMemory
        from backend.orchestration.state import Task, TaskType, LangGraphState, OrchestrationStep
        print("✅ All imports successful")
        
        # Test LLM adapter creation
        print("\n🤖 Testing LLM adapter creation...")
        llm_adapter = get_llm_adapter("mock", fallback=True)
        print(f"✅ LLM adapter created: {type(llm_adapter).__name__}")
        
        # Test memory creation
        print("\n🧠 Testing memory creation...")
        memory = GlobalMemory()
        print("✅ Memory created successfully")
        
        # Test CoCEO agent creation
        print("\n👑 Testing CoCEO agent creation...")
        co_ceo = CoCEOAgent(
            llm_adapter=llm_adapter,
            memory=memory,
            planning_temperature=0.3
        )
        print("✅ CoCEO agent created successfully")
        print(f"   - Agent name: {co_ceo.agent_name}")
        print(f"   - Capabilities: {co_ceo.capabilities}")
        print(f"   - Available agents: {co_ceo.available_agents}")
        
        # Test task creation
        print("\n📋 Testing task creation...")
        test_task = Task(
            description="Test task for agent validation",
            task_type=TaskType.REASONING,
            assigned_agent="CoCEO"
        )
        print(f"✅ Task created: {test_task.task_id}")
        print(f"   - Description: {test_task.description}")
        print(f"   - Type: {test_task.task_type}")
        print(f"   - Metadata: {test_task.metadata}")
        
        # Test state creation
        print("\n🔄 Testing state creation...")
        test_state = LangGraphState(
            user_input="Test user input for validation",
            tasks=[test_task],
            current_step=OrchestrationStep.PLANNING
        )
        print("✅ LangGraphState created successfully")
        print(f"   - User input: {test_state.user_input[:50]}...")
        print(f"   - Current step: {test_state.current_step}")
        print(f"   - Tasks count: {len(test_state.tasks)}")
        
        # Test CoCEO methods (without actual LLM calls)
        print("\n🎯 Testing CoCEO methods...")
        
        # Test strategic context gathering (will use mock data)
        print("   - Testing strategic context gathering...")
        strategic_context = await co_ceo.gather_strategic_context(
            "test query",
            {"search_filters": {"test": "value"}}
        )
        print(f"     ✅ Strategic context keys: {list(strategic_context.keys())}")
        
        # Test agent coordination
        print("   - Testing agent coordination...")
        coordination_result = await co_ceo.coordinate_agents(
            [test_task],
            ["CoCEO", "Finance", "Marketing"],
            test_state
        )
        print(f"     ✅ Coordination keys: {list(coordination_result.keys())}")
        print(f"     ✅ Assignments: {coordination_result['assignments']}")
        
        # Test tool call placeholder
        print("   - Testing tool call placeholder...")
        tool_result = await co_ceo.call_tool("web_search", {"query": "test"})
        print(f"     ✅ Tool result status: {tool_result['status']}")
        
        # Test memory integration
        print("\n🧠 Testing memory integration...")
        memory_count_before = len(memory.memory)
        
        # Add some test memory
        from backend.orchestration.memory import MemoryUnits
        test_memory = MemoryUnits(
            content="Test memory content",
            agent="CoCEO",
            task_id=test_task.task_id,
            current_step=OrchestrationStep.PLANNING,
            tags=["test", "validation"]
        )
        memory.add_memory(test_memory)
        
        memory_count_after = len(memory.memory)
        print(f"✅ Memory added: {memory_count_after - memory_count_before} new entries")
        
        # Test memory retrieval
        retrieved_memories = memory.get_relevant_memory({
            "agent": "CoCEO",
            "tags": ["test"]
        })
        print(f"✅ Memory retrieval: {len(retrieved_memories)} memories found")
        
        print("\n🎉 All tests passed successfully!")
        print("✅ Agents implementation is working correctly")
        return True
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    try:
        success = asyncio.run(test_agents_implementation())
        return 0 if success else 1
    except KeyboardInterrupt:
        print("\n⚠️ Test interrupted by user")
        return 1
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
