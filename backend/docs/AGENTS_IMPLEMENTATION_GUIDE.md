# Agents Implementation Guide

## 📋 Overview

This guide provides comprehensive documentation for the multi-agent orchestration system implementation in `backend/agents/`. The system follows an **orchestration-first architecture** where agents collaborate through centralized planning and coordination.

## 🏗️ Architecture & Design Principles

### Core Principles

1. **Inheritance-Based Design**: All agents inherit from `BaseAgent` for consistent behavior
2. **Memory Integration**: Full traceability through shared memory system
3. **Strategic Intelligence**: RAG-powered context gathering for informed decisions
4. **Rule-Based Assignment**: Simple but extensible agent assignment logic
5. **Error Resilience**: Comprehensive fallback strategies and error handling
6. **Tool-Ready**: Prepared for future tool system integration

### Agent Hierarchy

```
BaseAgent (Abstract)
├── CoCEOAgent (Primary Orchestrator)
├── FinanceAgent (TODO - Domain Specialist)
└── MarketingAgent (TODO - Domain Specialist)
```

## 🧠 BaseAgent Implementation

### Purpose
Provides the foundational framework for all agents in the system, handling:
- Task execution lifecycle
- Memory integration
- LLM interaction patterns
- Error handling and recovery
- State management

### Key Methods

#### `async def run(task: Task, context: LangGraphState) -> str`
**The core agent execution method that all agents inherit.**

**Execution Flow:**
1. **Type Safety**: Validates input parameters
2. **Information Extraction**: Gets task metadata and context
3. **Memory Retrieval**: Fetches relevant historical context
4. **Message Building**: Constructs LLM prompts with context
5. **LLM Interaction**: Calls the language model with error handling
6. **Memory Storage**: Stores results with appropriate tags
7. **State Injection**: Updates LangGraphState with outputs

**Error Handling:**
- Catches LLM failures and logs them
- Stores error information in memory for debugging
- Re-raises exceptions for upstream handling

### Memory Integration
- **Tags Used**: `["llm_output", "error", "llm_failure"]`
- **Context Retrieval**: Filters by task_id and agent name
- **Error Tracking**: Full audit trail of failures

## 🎯 CoCEOAgent Implementation

### Purpose
The **primary orchestrator** responsible for:
- High-level planning and task decomposition
- Strategic decision making with RAG context
- Multi-agent coordination and assignment
- Direct tool usage for informed planning
- Plan evaluation and adjustment

### Core Components

#### 1. Initialization & Configuration
```python
def __init__(
    llm_adapter: LLMAdapter,
    memory: Optional[GlobalMemory] = None,
    planner: Optional[OrchestrationPlanner] = None,
    knowledge_base: Optional[KnowledgeBaseService] = None,
    planning_temperature: float = 0.3
)
```

**Key Configuration:**
- **Planning Temperature**: 0.3 for structured, consistent planning
- **Auto-initialization**: Services created if not provided
- **Agent Assignment Rules**: TaskType → Agent mapping
- **Available Agents**: ["CoCEO", "Finance", "Marketing"]

#### 2. Agent Assignment Rules (Rule-Based PoC)
```python
self.agent_assignment_rules = {
    TaskType.REASONING: "CoCEO",
    TaskType.TOOLS: "CoCEO",
    TaskType.ACTION: "Finance"
}
```

**Assignment Strategy:**
1. **Rule-based Mapping**: Direct TaskType → Agent
2. **Keyword Matching**: Domain-specific term detection
3. **Context Awareness**: Uses orchestration state for decisions
4. **Fallback Logic**: CoCEO → First Available → Default

**Domain Keywords:**
- **Finance**: "finance", "cost", "budget", "revenue", "profit"
- **Marketing**: "marketing", "campaign", "brand", "customer", "promotion"

### Primary Methods

#### `async def plan_execution(user_input, context) -> Tuple[List[Task], str]`
**The main planning orchestration method.**

**Process:**
1. **Service Initialization**: Auto-setup of planner and knowledge base
2. **Memory Logging**: Records planning decisions with strategic tags
3. **Plan Generation**: Delegates to OrchestrationPlanner
4. **Strategic Enrichment**: Adds CoCEO-specific context and priorities
5. **Result Storage**: Logs completion with analysis

**Memory Tags**: `["plan_step", "strategic", "planning_result"]`

#### `async def gather_strategic_context(query, context) -> Dict[str, Any]`
**Multi-source context gathering for informed decision-making.**

**Context Sources:**
- **RAG Results**: Knowledge base search (limit: 5 documents)
- **Memory Context**: Past strategic decisions and plans
- **Web Results**: Placeholder for future tool integration
- **Search Context**: Uses provided context filters

**Output Structure:**
```python
{
    "rag_results": [...],
    "web_results": [...],
    "memory_context": [...],
    "timestamp": "ISO format",
    "search_context": {...}
}
```

#### `async def coordinate_agents(plan, available_agents, context) -> Dict[str, Any]`
**Strategic coordination of multi-agent task execution.**

**Coordination Process:**
1. **Task Assignment**: Uses optimal agent determination
2. **Dependency Tracking**: Maintains task relationships
3. **Execution Order**: Topological sort with priority
4. **Strategic Notes**: Bottleneck detection and analysis
5. **Memory Storage**: All assignments logged

**Output Structure:**
```python
{
    "assignments": {"task_id": "agent_name"},
    "execution_order": ["task_id_1", "task_id_2"],
    "dependencies": {"task_id": ["dep1", "dep2"]},
    "strategic_notes": "Analysis string",
    "estimated_duration": 0
}
```

#### `async def make_strategic_decision(decision_context, options, context) -> Dict[str, Any]`
**High-level strategic decision making with LLM reasoning.**

**Decision Process:**
1. **Context Gathering**: RAG + Memory integration
2. **Prompt Building**: Structured decision prompts
3. **LLM Reasoning**: JSON-structured responses
4. **Response Parsing**: Robust extraction with fallbacks
5. **Memory Storage**: Decision rationale for future reference

### Advanced Features

#### Task Prioritization
**Priority Calculation Factors:**
- **Task Type**: REASONING (0.8) > TOOLS (0.6) > ACTION (0.4)
- **Dependencies**: +0.1 per dependency
- **Position**: Earlier tasks get slight boost
- **Range**: 0.0 to 1.0

#### Execution Order Algorithm
**Topological Sort with Enhancements:**
- Respects task dependencies
- Handles circular dependencies gracefully
- Sorts by priority within dependency levels
- Robust edge case handling

#### Strategic Task Enrichment
**Metadata Added to Tasks:**
```python
{
    "priority_score": float,
    "strategic_importance": "high|medium|low",
    "estimated_complexity": "high|medium|low",
    "enriched_by": "CoCEO",
    "enrichment_timestamp": "ISO format",
    "session_context_keys": [...]
}
```

### Memory Integration Strategy

#### Memory Tags Used
- `"plan_step"`: Planning decisions and analysis
- `"agent_assignment"`: Task assignment decisions
- `"strategic"`: High-level strategic decisions
- `"coordination"`: Multi-agent coordination events
- `"decision"`: Strategic decision outcomes

#### Memory Benefits
- **Learning**: Past decisions inform future planning
- **Context**: Rich context for LLM reasoning
- **Traceability**: Full audit trail of decisions
- **Debugging**: Error tracking and analysis

### Error Handling & Resilience

#### Fallback Strategies
1. **Planning Failures**: Simple single-task fallback
2. **Decision Parsing**: Structured fallback responses
3. **Service Failures**: Graceful degradation
4. **Memory Errors**: Continue without memory if needed

#### Error Logging
- All failures logged with context
- Memory storage of error information
- Structured error responses
- Retry logic where appropriate

### Tool Integration (Future-Ready)

#### Placeholder Implementation
```python
async def call_tool(tool_name: str, tool_args: Dict[str, Any]) -> Dict[str, Any]
```

**Features:**
- Clean interface for ToolRegistry integration
- Structured placeholder responses
- Full logging of tool calls
- Ready for web search, email, etc.

## 🔄 Integration Points

### Orchestration System Integration
- **State Management**: Uses LangGraphState utilities
- **Memory System**: Full integration with GlobalMemory
- **Planner Integration**: Leverages OrchestrationPlanner
- **Task Model**: Uses enhanced Task with metadata

### Future Extensions
**Designed for:**
- LLM-based agent assignment (replace rules)
- Capability-based matching
- Dynamic tool loading
- Learning from outcomes
- User preference tracking

## 🎯 Implementation Status

### ✅ Completed
- BaseAgent framework with full lifecycle
- CoCEOAgent with all core functionality
- Memory integration with strategic tagging
- Rule-based agent assignment
- Strategic context gathering
- Task prioritization and ordering
- Error handling and fallbacks
- Tool-ready architecture

### 🔄 Next Steps
1. **FinanceAgent**: Domain-specific financial reasoning
2. **MarketingAgent**: Marketing domain expertise
3. **Tool System**: ToolRegistry and concrete tools
4. **Enhanced Assignment**: LLM-based agent selection
5. **Learning**: Outcome-based improvement

## 📊 Key Metrics & Monitoring

### Performance Indicators
- Planning success rate
- Agent assignment accuracy
- Task completion rates
- Error frequency and types
- Memory utilization

### Debugging Support
- Full memory audit trails
- Structured error logging
- Decision rationale tracking
- Context preservation

---

This implementation provides a robust, scalable foundation for multi-agent orchestration with clear extension points for future enhancements.
