"""
RAG Timeout Decorators

This module provides timeout-enabled decorators for RAG operations
such as embedding, vector search, hybrid search, and knowledge base queries.
All decorators include retry logic and optional fallback support.
"""

import functools
import logging
from typing import Callable, Optional

from ..app.core.timeout import with_timeout_and_retry
from ..app.errors import TimeoutError

logger = logging.getLogger(__name__)


def create_rag_timeout_decorator(
    operation_type: str,
    default_timeout: float,
    default_max_attempts: int = 2,
    default_fallback: Optional[Callable] = None,
):
    """
    Create a custom timeout decorator for RAG operations.

    This is a decorator factory that returns a timeout-enabled decorator 
    with retry and optional fallback behavior, scoped to a specific RAG operation.

    Args:
        operation_type: A label like 'embedding' or 'vector_search' used in logs
        default_timeout: Timeout in seconds
        default_max_attempts: How many times to retry
        default_fallback: Optional fallback async function

    Returns:
        A decorator that wraps async functions with timeout, retry, and logging.
    """

    def decorator_factory(
        timeout_seconds: float = default_timeout,
        max_attempts: int = default_max_attempts,
        fallback_func: Optional[Callable] = default_fallback,
    ):
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            async def wrapper(*args, **kwargs):
                operation_name = f"{operation_type}.{func.__name__}"

                try:
                    return await with_timeout_and_retry(
                        func,
                        *args,
                        timeout_seconds=timeout_seconds,
                        operation_name=operation_name,
                        max_attempts=max_attempts,
                        **kwargs,
                    )
                except Exception as e:
                    logger.error(
                        f"{operation_type} operation {func.__name__} failed: {e}"
                    )

                    if fallback_func:
                        logger.info(f"Using fallback function for {func.__name__}")
                        try:
                            return await fallback_func(*args, **kwargs)
                        except Exception as fallback_error:
                            logger.error(
                                f"Fallback function also failed: {fallback_error}"
                            )

                    raise e

            return wrapper

        return decorator

    return decorator_factory


# ---------------------------------------------------------
# RAG-Specific Decorators (defined using the factory above)
# ---------------------------------------------------------

with_embedding_timeout_decorator = create_rag_timeout_decorator(
    operation_type="embedding", default_timeout=30
)

with_vector_search_timeout_decorator = create_rag_timeout_decorator(
    operation_type="vector_search", default_timeout=10
)

with_hybrid_search_timeout_decorator = create_rag_timeout_decorator(
    operation_type="hybrid_search", default_timeout=12
)

with_knowledge_base_timeout_decorator = create_rag_timeout_decorator(
    operation_type="knowledge_base", default_timeout=15
)
