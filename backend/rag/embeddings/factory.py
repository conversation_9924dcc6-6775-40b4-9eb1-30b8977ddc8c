"""
Embedding Factory

This module provides factory functions for creating embedding models with fallback options.
"""

import logging
import os
from typing import Literal

from ...app.config import get_settings
from .base import EmbeddingModel
from .huggingface import HuggingFaceEmbedding
from .openai import OpenAIEmbedding

# Set up logging
logger = logging.getLogger(__name__)


class EmbeddingFactory:
    """
    Smart factory that creates embedding models with automatic fallback options.

    WHAT IS THIS?
    -----------
    This is a "factory" class that helps you create embedding models without
    worrying about which specific implementation to use or what happens if
    your first choice fails.

    Think of it like a smart assistant that knows how to set up different
    embedding tools and can switch to Plan B if Plan A doesn't work.

    WHY USE THIS INSTEAD OF DIRECT CREATION?
    -------------------------------------
    1. Automatic Fallback: If your first choice fails (e.g., missing dependencies),
       it automatically tries another option
    2. Simplified Creation: One line of code instead of multiple conditional blocks
    3. Consistent Interface: All models are created with the same interface
    4. Error Handling: Better error messages and graceful degradation

    HOW IT WORKS:
    ----------
    1. You specify your preferred embedding provider (huggingface or openai)
    2. The factory tries to create that type of embedding model
    3. If it fails and fallback is enabled, it tries an alternative provider
    4. If all options fail, it raises an informative error

    USAGE EXAMPLE:
    ------------
    ```python
    # Try HuggingFace first, fall back to OpenAI if needed
    model = EmbeddingFactory.create(
        provider="huggingface",  # First choice
        fallback=True,           # Enable automatic fallback
        model_name="intfloat/e5-base-v2"  # Model-specific parameter
    )

    # Use the model without worrying about which type it actually is
    embedding = await model.embed_query("What is RAG?")
    ```
    """

    @staticmethod
    def create(
        provider: Literal["huggingface", "openai"] = "huggingface",
        fallback: bool = True,
        **kwargs  # These are passed to the specific embedding model constructor
    ) -> EmbeddingModel:
        """
        Create an embedding model with automatic fallback options.

        WHAT DOES THIS DO?
        ----------------
        This method creates an embedding model of your preferred type, with
        the option to automatically try a different type if your first choice
        fails (for example, if required packages aren't installed).

        It's like ordering a meal at a restaurant - if they're out of your
        first choice, they can suggest an alternative instead of just saying "no".

        PARAMETERS:
        ----------
        provider (Literal["huggingface", "openai"]): Which type of embedding model to create
            - "huggingface": Local model using sentence-transformers (default)
            - "openai": Cloud-based model using OpenAI's API

        fallback (bool): Whether to try alternatives if first choice fails
            - True: Try other providers if the requested one fails (default)
            - False: Raise an error if the requested provider fails

        **kwargs: Options to pass to the specific embedding model
            - These are forwarded to the constructor of the chosen model
            - Common options:
              * model_name: Name of the specific model to use
              * device: For HuggingFace, which hardware to use (cpu/cuda/mps)
              * api_key: For OpenAI, your API key
              * dimensions: For OpenAI, custom embedding dimensions

        RETURNS:
        -------
        EmbeddingModel: A ready-to-use embedding model
            - This will be an instance of a specific implementation
              (HuggingFaceEmbedding or OpenAIEmbedding)
            - You can use it without knowing which specific type it is

        RAISES:
        ------
        ValueError: If the provider is unknown and fallback=False
            - This happens if you specify a provider that doesn't exist

        ImportError: If required dependencies are missing and fallback=False
            - For HuggingFace: If sentence-transformers isn't installed
            - For OpenAI: If openai package isn't installed
            - For both: If neither package is installed

        EXAMPLES:
        --------
        1. Create a HuggingFace model (default):
           ```python
           model = EmbeddingFactory.create(model_name="intfloat/e5-base-v2")
           ```

        2. Create an OpenAI model with specific dimensions:
           ```python
           model = EmbeddingFactory.create(
               provider="openai",
               model_name="text-embedding-3-small",
               dimensions=512
           )
           ```

        3. Create a model with fallback disabled:
           ```python
           model = EmbeddingFactory.create(
               provider="huggingface",
               fallback=False  # Will raise error if HuggingFace fails
           )
           ```
        """
        try:
            if provider == "huggingface":
                return HuggingFaceEmbedding(**kwargs)
            elif provider == "openai":
                return OpenAIEmbedding(**kwargs)
            else:
                if fallback:
                    logger.warning(
                        f"Unknown provider '{provider}', falling back to huggingface"
                    )
                    return HuggingFaceEmbedding(**kwargs)
                else:
                    raise ValueError(f"Unknown embedding provider: {provider}")
        except ImportError as e:
            if not fallback:
                raise

            logger.warning(
                f"Provider '{provider}' unavailable: {str(e)}. "
                "Trying fallback providers."
            )

            # Try OpenAI if HuggingFace fails
            if provider == "huggingface":
                try:
                    return OpenAIEmbedding(**kwargs)
                except ImportError:
                    raise ImportError(
                        "No embedding providers available. "
                        "Install either sentence-transformers or openai."
                    )

            # Try HuggingFace if OpenAI fails
            elif provider == "openai":
                try:
                    return HuggingFaceEmbedding(**kwargs)
                except ImportError:
                    raise ImportError(
                        "No embedding providers available. "
                        "Install either sentence-transformers or openai."
                    )


def get_embedding_model(
    provider: str = None,
    model_name: str = None,
    fallback: bool = True,
    **kwargs
) -> EmbeddingModel:
    """
    Get an embedding model based on configuration or parameters.

    This function reads the configuration from the application settings
    and creates an appropriate embedding model.

    Args:
        provider: Embedding provider (huggingface, openai). If None, uses settings.
        model_name: Model name. If None, uses settings.
        fallback: Whether to enable fallback to other providers
        **kwargs: Additional arguments passed to the embedding model

    Returns:
        An initialized embedding model ready for use
    """
    settings = get_settings()

    # Use provided parameters or fall back to settings
    provider = provider or settings.DEFAULT_EMBEDDING_MODEL
    model_name = model_name or settings.EMBEDDING_MODEL

    logger.info(f"Creating embedding model of type {provider} with model {model_name}")

    # Create the embedding model using the factory
    return EmbeddingFactory.create(
        provider=provider,
        model_name=model_name,
        fallback=fallback,
        **kwargs
    )