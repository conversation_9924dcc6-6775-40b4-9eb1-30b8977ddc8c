"""
Retriever Component for RAG System

This module implements the retriever component of the RAG system, which is responsible for:
1. Processing and potentially rewriting user queries
2. Finding relevant documents using multiple search strategies
3. Combining results from different search methods
4. Ensuring retrieved context fits within the LLM's context window

The retriever coordinates between the query analyzer and knowledge base service
to provide enhanced retrieval capabilities.
"""

import logging
from typing import Dict, List, Optional, Any
from abc import ABC, abstractmethod

# Import optional dependencies
try:
    HAS_TIKTOKEN = True
    import tiktoken
except ImportError:
    HAS_TIKTOKEN = False

# Import timeout utilities
from ...app.core.timeout import with_timeout_and_retry
from ..timeout import (
    with_embedding_timeout_decorator,
    with_hybrid_search_timeout_decorator
)

# Import separate components
from .context_window import ContextWindowManager
from .query_rewriter import QueryRewriter

# Set up logging
logger = logging.getLogger(__name__)


class Retriever(ABC):
    """
    Base class for document retrieval.

    This abstract class defines the interface for all retrievers.
    Concrete implementations should override the retrieve method.
    """

    @abstractmethod
    async def retrieve(
        self,
        query: str,
        retrieval_strategy: Optional[Dict[str, Any]] = None,
        limit: int = 5,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Retrieve relevant documents for a query.

        Args:
            query: The query text
            retrieval_strategy: Optional strategy from query analyzer
            limit: Maximum number of results to return
            filters: Optional metadata filters

        Returns:
            List of dictionaries containing:
            - id: The document ID
            - text: The document text
            - metadata: The document metadata
            - score: The relevance score
        """
        raise NotImplementedError("Subclasses must implement retrieve")


class HybridRetriever(Retriever):
    """
    Retriever that combines vector similarity and keyword search.

    This class coordinates between the query analyzer and knowledge base service
    to provide enhanced retrieval results using multiple search methods.
    """

    def __init__(
        self,
        query_analyzer,
        knowledge_base_service,
        default_vector_weight: float = 0.7,
        default_keyword_weight: float = 0.3,
        **kwargs
    ):
        """
        Initialize the hybrid retriever.

        Args:
            query_analyzer: The query analyzer for determining retrieval strategies
            knowledge_base_service: The knowledge base service for search operations
            default_vector_weight: Default weight for vector similarity results (0.0 to 1.0)
            default_keyword_weight: Default weight for keyword search results (0.0 to 1.0)
            **kwargs: Additional arguments
        """
        self.query_analyzer = query_analyzer
        self.knowledge_base_service = knowledge_base_service

        # Ensure weights sum to 1.0
        total_weight = default_vector_weight + default_keyword_weight
        self.default_vector_weight = default_vector_weight / total_weight
        self.default_keyword_weight = default_keyword_weight / total_weight

        # Initialize optional components
        self.context_window_manager = kwargs.get("context_window_manager")
        self.query_rewriter = kwargs.get("query_rewriter")

    @with_hybrid_search_timeout_decorator(
        timeout_seconds=12,  # 12 seconds timeout for hybrid search
        max_attempts=2,      # 2 retry attempts
        fallback_func=None   # No fallback function, will return empty list on failure
    )
    async def retrieve(
        self,
        query: str,
        retrieval_strategy: Optional[Dict[str, Any]] = None,
        limit: int = 5,
        filters: Optional[Dict[str, Any]] = None
    ) -> List[Dict[str, Any]]:
        """
        Retrieve relevant documents using hybrid search.

        This method:
        1. Analyzes the query if no retrieval strategy is provided
        2. Applies the retrieval strategy to guide the search
        3. Performs search using the knowledge base service
        4. Enhances results with additional processing if needed

        Args:
            query: The query text
            retrieval_strategy: Optional strategy from query analyzer
            limit: Maximum number of results to return
            filters: Optional metadata filters

        Returns:
            List of dictionaries containing:
            - id: The document ID
            - text: The document text
            - metadata: The document metadata
            - score: The relevance score
        """
        try:
            # Rewrite query if query rewriter is available
            processed_query = query
            if self.query_rewriter:
                try:
                    # Create context for query rewriting
                    rewrite_context = {}

                    # Add retrieval strategy to context if available
                    if retrieval_strategy:
                        rewrite_context["retrieval_strategy"] = retrieval_strategy

                        # Extract department from filters if available
                        if "filters" in retrieval_strategy and "department" in retrieval_strategy["filters"]:
                            rewrite_context["department"] = retrieval_strategy["filters"]["department"]

                    # Call the enhanced query rewriter with context
                    processed_query = await with_timeout_and_retry(
                        self.query_rewriter.rewrite_query,
                        query,
                        rewrite_context,
                        timeout_seconds=5,  # 5 seconds timeout for query rewriting
                        operation_name="query_rewriter.rewrite_query",
                        max_attempts=2      # Try twice before giving up
                    )
                    logger.debug(f"Rewrote query: '{query}' -> '{processed_query}'")
                except Exception as e:
                    logger.warning(f"Query rewriting failed: {e}. Using original query.")
                    processed_query = query

            # Get retrieval strategy if not provided
            if retrieval_strategy is None:
                try:
                    analysis = await with_timeout_and_retry(
                        self.query_analyzer.analyze,
                        processed_query,
                        timeout_seconds=5,  # 5 seconds timeout for query analysis
                        operation_name="query_analyzer.analyze",
                        max_attempts=1      # Only try once, fall back to default strategy
                    )
                    retrieval_strategy = analysis.get("retrieval_strategy", {})
                    logger.debug(f"Query analysis: {analysis}")
                except Exception as e:
                    logger.warning(f"Query analysis failed: {e}. Using default strategy.")
                    retrieval_strategy = {}

            # Extract search parameters from strategy
            search_type = retrieval_strategy.get("search_type", "hybrid")
            strategy_limit = retrieval_strategy.get("limit", limit)
            strategy_filters = retrieval_strategy.get("filters", {})
            vector_weight = retrieval_strategy.get("weights", {}).get("vector", self.default_vector_weight)
            keyword_weight = retrieval_strategy.get("weights", {}).get("keyword", self.default_keyword_weight)

            # Merge filters - prioritize department_id from strategy_filters
            merged_filters = {}

            # Start with user-provided filters
            if filters:
                merged_filters.update(filters)

            # Add strategy filters, but handle department_id specially
            has_department_filter = False
            for key, value in strategy_filters.items():
                if key == "department_id" and isinstance(value, dict) and "$in" in value:
                    # Convert department_id filter to metadata.department filter for PostgreSQL
                    merged_filters["metadata.department"] = value
                    has_department_filter = True
                elif key == "department_id" and isinstance(value, dict) and "$eq" in value:
                    # Convert department_id filter to metadata.department filter for PostgreSQL
                    merged_filters["metadata.department"] = {"$eq": value["$eq"]}
                    has_department_filter = True
                elif key == "department":
                    # Convert department filter to metadata.department filter for PostgreSQL
                    if isinstance(value, dict) and "$in" in value:
                        merged_filters["metadata.department"] = value
                    else:
                        merged_filters["metadata.department"] = {"$eq": value}
                    has_department_filter = True
                else:
                    merged_filters[key] = value

            # Remove any conflicting department filters
            if has_department_filter and "department_id" in merged_filters:
                del merged_filters["department_id"]
            if has_department_filter and "department" in merged_filters:
                del merged_filters["department"]

            logger.debug(f"Merged filters: {merged_filters}")

            # Perform search using knowledge base service with timeout
            results = await with_timeout_and_retry(
                self.knowledge_base_service.search,
                query=processed_query,
                collection="knowledge",
                limit=strategy_limit,
                search_type=search_type,
                filters=merged_filters,
                vector_weight=vector_weight,
                keyword_weight=keyword_weight,
                timeout_seconds=10,  # 10 seconds timeout for search
                operation_name=f"knowledge_base_service.search.{search_type}",
                max_attempts=2       # Try twice before giving up
            )

            # Fit to context window if manager is available
            if self.context_window_manager and results:
                try:
                    results = await with_timeout_and_retry(
                        self.context_window_manager.fit_to_context_window,
                        query=processed_query,
                        documents=results,
                        timeout_seconds=3,  # 3 seconds timeout for context window fitting
                        operation_name="context_window_manager.fit_to_context_window",
                        max_attempts=1      # Only try once, fall back to original results
                    )
                except Exception as e:
                    logger.warning(f"Context window fitting failed: {e}. Using original results.")

            return results

        except Exception as e:
            logger.error(f"Retrieval failed: {e}", exc_info=True)
            # Return empty results on failure
            return []


# ContextWindowManager moved to separate module to avoid duplication


# QueryRewriter moved to separate module to avoid duplication