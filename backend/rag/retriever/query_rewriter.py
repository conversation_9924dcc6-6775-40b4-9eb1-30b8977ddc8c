"""
Query Rewriter Utility

This module provides utilities for rewriting queries to improve retrieval performance.
It can be used by agents or orchestration logic before retrieval.
"""

import logging
from typing import Dict, List, Any, Optional, Set

from ...app.core.timeout import with_timeout_and_retry
from ..timeout import with_embedding_timeout_decorator

# Set up logging
logger = logging.getLogger(__name__)


class QueryRewriter:
    """
    Rewrites queries to improve retrieval performance.

    This class uses the LLM to rewrite queries for better retrieval results.
    It implements several strategies for query enhancement:
    1. Key term extraction and expansion
    2. Context-aware query reformulation
    3. Fallback mechanisms for when rewriting fails
    """

    def __init__(self, llm_adapter, embedding_model=None, knowledge_base_service=None):
        """
        Initialize the query rewriter.

        Args:
            llm_adapter: The LLM adapter to use for query rewriting
            embedding_model: Optional embedding model for semantic analysis
            knowledge_base_service: Optional knowledge base service for context-aware rewriting
        """
        self.llm_adapter = llm_adapter
        self.embedding_model = embedding_model
        self.knowledge_base_service = knowledge_base_service

        # Common terms to avoid expanding (stopwords, common business terms)
        self.common_terms = {
            "the", "and", "or", "a", "an", "in", "on", "at", "to", "for", "with", "by",
            "business", "company", "team", "project", "report", "meeting", "email",
            "plan", "strategy", "goal", "objective", "target", "result", "outcome"
        }

        # Common acronyms and their expansions
        self.acronyms = {
            "roi": "return on investment",
            "kpi": "key performance indicator",
            "ctr": "click-through rate",
            "cac": "customer acquisition cost",
            "ltv": "lifetime value",
            "arpu": "average revenue per user",
            "mrr": "monthly recurring revenue",
            "arr": "annual recurring revenue",
            "cro": "conversion rate optimization",
            "seo": "search engine optimization",
            "sem": "search engine marketing",
            "ppc": "pay per click",
            "cpa": "cost per acquisition",
            "cpc": "cost per click",
            "cpm": "cost per mille",
            "b2b": "business to business",
            "b2c": "business to consumer",
            "d2c": "direct to consumer",
            "saas": "software as a service",
            "q1": "first quarter",
            "q2": "second quarter",
            "q3": "third quarter",
            "q4": "fourth quarter",
            "fy": "fiscal year",
            "yoy": "year over year",
            "mom": "month over month",
            "qoq": "quarter over quarter",
            "ebitda": "earnings before interest taxes depreciation and amortization",
            "cogs": "cost of goods sold",
            "p&l": "profit and loss",
            "r&d": "research and development",
            "hr": "human resources",
            "pr": "public relations",
            "ceo": "chief executive officer",
            "cfo": "chief financial officer",
            "cmo": "chief marketing officer",
            "cto": "chief technology officer",
            "coo": "chief operating officer"
        }

    @with_embedding_timeout_decorator(timeout_seconds=5, max_attempts=2)
    async def rewrite_query(self, query: str, context: Dict[str, Any] = None) -> str:
        """
        Rewrite a query to improve retrieval performance.

        This method uses a multi-strategy approach:
        1. First tries simple rule-based enhancements (acronym expansion, etc.)
        2. Then attempts LLM-based rewriting if available
        3. Falls back to the original query with minimal enhancements if needed

        Args:
            query: The original query
            context: Optional context to guide rewriting (e.g., user info, conversation history)

        Returns:
            Rewritten query
        """
        # Start with the original query
        enhanced_query = query.strip()

        try:
            # Step 1: Apply rule-based enhancements
            rule_based_query = await self._apply_rule_based_enhancements(enhanced_query)

            # If rule-based enhancements made significant changes, use that
            if self._is_significantly_different(enhanced_query, rule_based_query):
                enhanced_query = rule_based_query
                logger.debug(f"Rule-based query enhancement: '{query}' -> '{enhanced_query}'")

            # Step 2: Try LLM-based rewriting
            llm_rewritten_query = await self._llm_rewrite_query(enhanced_query, context)

            # If LLM rewriting was successful and made significant changes, use that
            if llm_rewritten_query and self._is_significantly_different(enhanced_query, llm_rewritten_query):
                enhanced_query = llm_rewritten_query
                logger.debug(f"LLM-based query rewriting: '{query}' -> '{enhanced_query}'")

            # Step 3: Try context-aware rewriting if knowledge base service is available
            if self.knowledge_base_service and context:
                context_aware_query = await self._context_aware_rewrite(enhanced_query, context)

                # If context-aware rewriting made significant changes, use that
                if context_aware_query and self._is_significantly_different(enhanced_query, context_aware_query):
                    enhanced_query = context_aware_query
                    logger.debug(f"Context-aware query rewriting: '{query}' -> '{enhanced_query}'")

            return enhanced_query

        except Exception as e:
            logger.error(f"Error rewriting query: {e}", exc_info=True)
            # Fall back to original query with basic enhancements
            return await self._fallback_rewrite(query)

    async def _apply_rule_based_enhancements(self, query: str) -> str:
        """
        Apply rule-based enhancements to the query.

        Args:
            query: The original query

        Returns:
            Enhanced query
        """
        # Convert to lowercase for processing
        query_lower = query.lower()

        # Check for acronyms directly in the query
        words = query_lower.split()
        has_expanded_acronyms = False
        expanded_acronyms = []

        for word in words:
            # Clean the word from punctuation
            clean_word = word.strip(".,?!:;()")
            if clean_word in self.acronyms:
                expanded_acronyms.append(f"{clean_word} ({self.acronyms[clean_word]})")
                has_expanded_acronyms = True
            else:
                expanded_acronyms.append(word)

        # If we expanded any acronyms, use the expanded version
        if has_expanded_acronyms:
            enhanced_query = f"{query} ({' '.join(self.acronyms[w.strip('.,?!:;()')] for w in words if w.strip('.,?!:;()') in self.acronyms)})"
        else:
            # Extract terms from the query
            terms = self._extract_terms(query_lower)

            # Expand acronyms in extracted terms
            expanded_terms = []
            for term in terms:
                if term.lower() in self.acronyms:
                    expanded_terms.append(self.acronyms[term.lower()])
                else:
                    expanded_terms.append(term)

            # Combine original query with expanded terms
            if len(expanded_terms) > len(terms):
                enhanced_query = f"{query} ({' '.join(expanded_terms)})"
            else:
                enhanced_query = query

        return enhanced_query

    async def _llm_rewrite_query(self, query: str, context: Dict[str, Any] = None) -> Optional[str]:
        """
        Use the LLM to rewrite the query.

        Args:
            query: The query to rewrite
            context: Optional context to guide rewriting

        Returns:
            Rewritten query or None if rewriting failed
        """
        # Construct context-aware prompt if context is provided
        context_str = ""
        if context:
            if "user_info" in context:
                context_str += f"\nUser Information: {context['user_info']}"
            if "conversation_history" in context:
                context_str += f"\nConversation History: {context['conversation_history']}"
            if "department" in context:
                context_str += f"\nDepartment Focus: {context['department']}"

        prompt = [
            {
                "role": "system",
                "content": (
                    "You are a query rewriting assistant. Your task is to rewrite search queries "
                    "to improve retrieval performance. Focus on:"
                    "\n1. Expanding acronyms"
                    "\n2. Adding synonyms for important terms"
                    "\n3. Making implicit information explicit"
                    "\n4. Removing unnecessary words"
                    "\n5. Focusing on key concepts"
                    "\n6. Extracting specific entities, metrics, or time periods"
                    "\n7. Adding domain-specific terminology"
                    f"{context_str}"
                    "\nProvide ONLY the rewritten query without explanation."
                )
            },
            {
                "role": "user",
                "content": f"Original query: {query}\n\nRewritten query:"
            }
        ]

        try:
            # Use timeout and retry for LLM call
            rewritten_query = await with_timeout_and_retry(
                self.llm_adapter.chat,
                prompt,
                timeout_seconds=5,  # 5 seconds timeout for LLM call
                operation_name="llm_adapter.chat_for_query_rewrite",
                max_attempts=2      # Try twice before giving up
            )
            return rewritten_query.strip()
        except Exception as e:
            logger.warning(f"LLM-based query rewriting failed: {e}")
            return None

    async def _context_aware_rewrite(self, query: str, context: Dict[str, Any]) -> Optional[str]:
        """
        Rewrite the query based on available context.

        Args:
            query: The query to rewrite
            context: Context to guide rewriting

        Returns:
            Context-aware rewritten query or None if rewriting failed
        """
        if not self.knowledge_base_service:
            return None

        try:
            # Extract department if available
            department = context.get("department")

            # Perform a preliminary search to get relevant terms
            filters = {"department": department} if department else {}
            preliminary_results = await with_timeout_and_retry(
                self.knowledge_base_service.search,
                query=query,
                collection="knowledge",
                limit=3,
                search_type="keyword",
                filters=filters,
                timeout_seconds=3,
                operation_name="knowledge_base_service.search_for_query_rewrite",
                max_attempts=1
            )

            if not preliminary_results:
                return None

            # Extract key terms from the preliminary results
            key_terms = set()
            for result in preliminary_results:
                # Extract terms from the document title and content
                if "metadata" in result and "title" in result["metadata"]:
                    title_terms = self._extract_terms(result["metadata"]["title"])
                    key_terms.update([t for t in title_terms if t.lower() not in self.common_terms])

                if "text" in result:
                    # Only use the first 200 characters to extract key terms
                    content_sample = result["text"][:200]
                    content_terms = self._extract_terms(content_sample)
                    key_terms.update([t for t in content_terms if t.lower() not in self.common_terms])

            # Filter out terms already in the query
            query_terms = set(self._extract_terms(query.lower()))
            new_terms = [term for term in key_terms if term.lower() not in query_terms]

            # If we found new terms, enhance the query
            if new_terms:
                # Take at most 3 new terms to avoid query explosion
                selected_terms = new_terms[:3]
                enhanced_query = f"{query} {' '.join(selected_terms)}"
                return enhanced_query

            return None

        except Exception as e:
            logger.warning(f"Context-aware query rewriting failed: {e}")
            return None

    async def _fallback_rewrite(self, query: str) -> str:
        """
        Apply minimal enhancements when other methods fail.

        Args:
            query: The original query

        Returns:
            Minimally enhanced query
        """
        # Just expand acronyms as a last resort
        query_lower = query.lower()
        words = query_lower.split()

        # Check for acronyms
        for i, word in enumerate(words):
            if word in self.acronyms:
                words[i] = self.acronyms[word]

        # If changes were made, return the enhanced query
        enhanced_query = " ".join(words)
        if enhanced_query != query_lower:
            return f"{query} ({enhanced_query})"

        # Otherwise, return the original query
        return query

    def _extract_terms(self, text: str) -> List[str]:
        """
        Extract meaningful terms from text.

        Args:
            text: The text to extract terms from

        Returns:
            List of extracted terms
        """
        # Simple whitespace tokenization
        words = text.split()

        # Clean words from punctuation
        cleaned_words = [word.strip(".,?!:;()") for word in words]

        # Filter out common terms and very short terms
        filtered_words = [word for word in cleaned_words if word.lower() not in self.common_terms and len(word) > 2]

        # Extract multi-word phrases (simple approach)
        phrases = []
        for i in range(len(cleaned_words) - 1):
            word1 = cleaned_words[i].lower()
            word2 = cleaned_words[i+1].lower()

            if word1 not in self.common_terms and word2 not in self.common_terms and len(word1) > 2 and len(word2) > 2:
                phrases.append(f"{cleaned_words[i]} {cleaned_words[i+1]}")

        # Extract three-word phrases for better coverage
        for i in range(len(cleaned_words) - 2):
            word1 = cleaned_words[i].lower()
            word2 = cleaned_words[i+1].lower()
            word3 = cleaned_words[i+2].lower()

            if (word1 not in self.common_terms and
                word3 not in self.common_terms and
                len(word1) > 2 and len(word3) > 2):
                phrases.append(f"{cleaned_words[i]} {cleaned_words[i+1]} {cleaned_words[i+2]}")

        # Combine single words and phrases
        all_terms = filtered_words + phrases

        # Remove duplicates and limit the number of terms
        unique_terms = list(dict.fromkeys(all_terms))
        return unique_terms[:15]  # Limit to 15 terms

    def _is_significantly_different(self, original: str, rewritten: str) -> bool:
        """
        Check if the rewritten query is significantly different from the original.

        Args:
            original: The original query
            rewritten: The rewritten query

        Returns:
            True if significantly different, False otherwise
        """
        if not rewritten or rewritten == original:
            return False

        # Check if length difference is significant
        if len(rewritten) < len(original) * 0.8 or len(rewritten) > len(original) * 2.0:
            return True

        # Check if term difference is significant
        original_terms = set(self._extract_terms(original.lower()))
        rewritten_terms = set(self._extract_terms(rewritten.lower()))

        # Calculate Jaccard similarity
        intersection = len(original_terms.intersection(rewritten_terms))
        union = len(original_terms.union(rewritten_terms))

        if union == 0:
            return False

        similarity = intersection / union

        # If similarity is low, queries are significantly different
        return similarity < 0.7


async def get_query_rewriter(
    llm_adapter,
    embedding_model=None,
    knowledge_base_service=None
) -> QueryRewriter:
    """
    Get a query rewriter instance.
    
    Args:
        llm_adapter: The LLM adapter to use for query rewriting
        embedding_model: Optional embedding model for semantic analysis
        knowledge_base_service: Optional knowledge base service for context-aware rewriting
        
    Returns:
        QueryRewriter instance
    """
    return QueryRewriter(
        llm_adapter=llm_adapter,
        embedding_model=embedding_model,
        knowledge_base_service=knowledge_base_service
    )