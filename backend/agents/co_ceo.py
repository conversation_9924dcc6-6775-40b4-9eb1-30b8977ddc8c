"""
CoCEO Agent - Primary Orchestrator

The CoCEO agent acts as the primary orchestrator that:
- Interprets high-level user input
- Generates orchestration plans using the planner
- Can directly use tools (RAG, web search) to inform decisions
- Coordinates with other agents
- Makes strategic decisions about task delegation
"""

import logging
import json
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime

# Import base agent
from .base import BaseAgent

# Import orchestration components
from ..orchestration.state import Task, TaskType, LangGraphState, OrchestrationStep
from ..orchestration.planner import OrchestrationPlanner
from ..orchestration.memory import GlobalMemory, MemoryUnits

# Import LLM components
from ..llm.base import LLMAdapter, ChatMessage

# Import RAG components
from ..rag import KnowledgeBaseService, initialize_knowledge_base_service

logger = logging.getLogger(__name__)


class CoCEOAgent(BaseAgent):
    """
    CoCEO Agent - Primary orchestrator for multi-agent workflows.
    
    Responsibilities:
    - High-level planning and task decomposition
    - Strategic decision making
    - Direct tool usage for informed planning
    - Coordination with other agents
    - Plan evaluation and adjustment
    """
    
    def __init__(
        self,
        llm_adapter: LLMAdapter,
        memory: Optional[GlobalMemory] = None,
        planner: Optional[OrchestrationPlanner] = None,
        knowledge_base: Optional[KnowledgeBaseService] = None,
        planning_temperature: float = 0.3,  # Lower temperature for more structured planning
    ):
        super().__init__(llm_adapter, memory)
        
        # CoCEO-specific components
        self.planner = planner  # Will be initialized if None
        self.knowledge_base = knowledge_base  # Will be initialized if None
        self.planning_temperature = planning_temperature
        
        # CoCEO identity and capabilities
        self.agent_name = "CoCEO"
        self.capabilities = [
            "strategic_planning",
            "task_decomposition", 
            "agent_coordination",
            "direct_tool_usage",
            "plan_evaluation"
        ]
        
        # Agent assignment rules (rule-based for PoC)
        self.agent_assignment_rules = {
            TaskType.REASONING: "CoCEO",
            TaskType.TOOLS: "CoCEO",  # CoCEO can use tools directly
            TaskType.ACTION: "Finance",  # Default to Finance for actions
        }
        
        # Available agents for coordination
        self.available_agents = ["CoCEO", "Finance", "Marketing"]

    async def plan_execution(
        self, 
        user_input: str, 
        context: Optional[Dict[str, Any]] = None
    ) -> Tuple[List[Task], str]:
        """
        Generate a comprehensive execution plan for the user's request.
        
        This is the CoCEO's primary responsibility - taking high-level user input
        and breaking it down into actionable tasks for the multi-agent system.
        
        Args:
            user_input: The user's request / prompt 
            context: Additional context (previous conversations, user preferences, etc.)
            
        Returns:
            Tuple of (List of Task objects, Analysis string explaining the plan)
        """
        logger.info(f"[CoCEO] Starting execution planning for: {user_input[:100]}...")
        
        try:
            # Initialize services if needed
            await self._initialize_services()
            
            # Store planning decision in memory
            if self.memory:
                planning_memory = MemoryUnits(
                    content=f"Starting execution planning for: {user_input}",
                    agent=self.agent_name,
                    task_id="planning_start",
                    current_step=OrchestrationStep.PLANNING,
                    tags=["plan_step", "strategic"]
                )
                self.memory.add_memory(planning_memory)
            
            # Use the planner to generate structured tasks
            tasks, analysis = await self.planner.generate_plan(user_input, context)
            
            # Add CoCEO-specific enrichment to the plan
            enriched_tasks = await self._enrich_plan_with_strategic_context(
                tasks, user_input, context
            )
            
            # Store planning result in memory
            if self.memory:
                result_memory = MemoryUnits(
                    content=f"Generated plan with {len(enriched_tasks)} tasks: {analysis[:200]}...",
                    agent=self.agent_name,
                    task_id="planning_complete",
                    current_step=OrchestrationStep.PLANNING,
                    tags=["plan_step", "strategic", "planning_result"]
                )
                self.memory.add_memory(result_memory)
            
            logger.info(f"[CoCEO] Generated plan with {len(enriched_tasks)} tasks")
            return enriched_tasks, analysis
            
        except Exception as e:
            logger.error(f"[CoCEO] Error in execution planning: {e}")
            # Return a fallback plan
            return await self._generate_fallback_plan(user_input, context)

    async def gather_strategic_context(
        self,
        query: str,
        context: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Gather strategic context using direct tool access (RAG, web search, etc.).

        This allows the CoCEO to make informed decisions by accessing
        relevant information before planning or coordinating.

        Args:
            query: The information need
            context: Additional context for the search

        Returns:
            Dictionary containing gathered context from various sources
        """
        logger.info(f"[CoCEO] Gathering strategic context for: {query}")

        # Use provided context for enhanced search
        context_info = context or {}
        search_filters = context_info.get("search_filters", {})

        strategic_context = {
            "rag_results": [],
            "web_results": [],  # Will be implemented when web search tool is ready
            "memory_context": [],
            "timestamp": datetime.now().isoformat(),
            "search_context": search_filters
        }
        
        try:
            # Initialize services if needed
            await self._initialize_services()
            
            # Gather RAG context
            if self.knowledge_base:
                rag_results = await self.knowledge_base.search(
                    query=query,
                    limit=5,
                    collection=None  # Search across all collections
                )
                strategic_context["rag_results"] = rag_results
                logger.debug(f"[CoCEO] Retrieved {len(rag_results)} RAG results")
            
            # Gather memory context
            if self.memory:
                memory_filters = {"agent": self.agent_name, "any_tags": ["strategic", "plan_step"], "limit": 5}
                memory_results = self.memory.get_relevant_memory(memory_filters)
                strategic_context["memory_context"] = [
                    {"content": m.content, "timestamp": m.timestamp.isoformat(), "tags": m.tags}
                    for m in memory_results
                ]
                logger.debug(f"[CoCEO] Retrieved {len(memory_results)} memory items")
            
            # TODO: Add web search when tool system is implemented
            # strategic_context["web_results"] = await self.call_tool("web_search", {"query": query})
            
            return strategic_context
            
        except Exception as e:
            logger.error(f"[CoCEO] Error gathering strategic context: {e}")
            return strategic_context

    async def coordinate_agents(
        self, 
        plan: List[Task], 
        available_agents: Optional[List[str]] = None,
        context: Optional[LangGraphState] = None
    ) -> Dict[str, Any]:
        """
        Coordinate task assignment and execution across multiple agents.
        
        This method handles the strategic coordination of other agents,
        making decisions about task assignment, dependencies, and execution order.
        
        Args:
            plan: List of tasks to be executed
            available_agents: List of available agent names (uses default if None)
            context: Current orchestration state
            
        Returns:
            Dictionary containing coordination decisions and assignments
        """
        if available_agents is None:
            available_agents = self.available_agents
            
        logger.info(f"[CoCEO] Coordinating {len(plan)} tasks across {len(available_agents)} agents")
        
        coordination_result = {
            "assignments": {},
            "execution_order": [],
            "dependencies": {},
            "strategic_notes": "",
            "estimated_duration": 0
        }
        
        try:
            # Analyze tasks and make strategic assignments
            for task in plan:
                # Determine best agent for this task
                assigned_agent = await self._determine_optimal_agent(
                    task, available_agents, context
                )
                
                # Update task assignment
                task.assigned_agent = assigned_agent
                coordination_result["assignments"][task.task_id] = assigned_agent
                
                # Track dependencies
                if task.dependencies:
                    coordination_result["dependencies"][task.task_id] = list(task.dependencies)
                
                # Store assignment decision in memory
                if self.memory:
                    assignment_memory = MemoryUnits(
                        content=f"Assigned task {task.task_id} ({task.task_type}) to {assigned_agent}: {task.description[:100]}",
                        agent=self.agent_name,
                        task_id=task.task_id,
                        current_step=OrchestrationStep.TASK_ASSIGNMENT,
                        tags=["agent_assignment", "coordination"]
                    )
                    self.memory.add_memory(assignment_memory)
            
            # Determine execution order based on dependencies
            coordination_result["execution_order"] = self._calculate_execution_order(plan)
            
            # Generate strategic notes
            coordination_result["strategic_notes"] = await self._generate_coordination_notes(
                plan, available_agents, context
            )
            
            logger.info(f"[CoCEO] Coordination complete: {len(coordination_result['assignments'])} assignments")
            return coordination_result
            
        except Exception as e:
            logger.error(f"[CoCEO] Error in agent coordination: {e}")
            return coordination_result

    async def make_strategic_decision(
        self,
        decision_context: Dict[str, Any],
        options: List[Dict[str, Any]],
        context: Optional[LangGraphState] = None
    ) -> Dict[str, Any]:
        """
        Make strategic decisions using LLM reasoning and available context.

        This method handles high-level strategic decisions that require
        the CoCEO's oversight and reasoning capabilities.

        Args:
            decision_context: Context for the decision
            options: Available options to choose from
            context: Current orchestration state

        Returns:
            Dictionary containing the decision and reasoning
        """
        logger.info(f"[CoCEO] Making strategic decision with {len(options)} options")

        try:
            # Gather additional context if needed
            strategic_context = await self.gather_strategic_context(
                decision_context.get("query", ""),
                decision_context
            )

            # Build decision prompt
            messages = self._build_decision_messages(
                decision_context, options, strategic_context, context
            )

            # Get LLM reasoning
            decision_response = await self.llm_adapter.chat(
                messages=messages,
                temperature=self.planning_temperature,
                max_tokens=1000
            )

            # Parse and structure the decision
            decision_result = await self._parse_strategic_decision(
                decision_response, options, decision_context
            )

            # Store decision in memory for future reference
            if self.memory:
                await self._store_strategic_decision(decision_result, context)

            logger.info(f"[CoCEO] Strategic decision made: {decision_result.get('choice', 'unknown')}")
            return decision_result

        except Exception as e:
            logger.error(f"[CoCEO] Error in strategic decision making: {e}")
            return {
                "choice": options[0] if options else {},
                "reasoning": f"Fallback decision due to error: {e}",
                "confidence": 0.1
            }

    # Placeholder for future tool integration
    async def call_tool(self, tool_name: str, tool_args: Dict[str, Any]) -> Dict[str, Any]:
        """
        Placeholder method for calling tools through the future ToolRegistry.

        Args:
            tool_name: Name of the tool to call (e.g., "web_search", "email")
            tool_args: Arguments to pass to the tool

        Returns:
            Tool execution results
        """
        logger.info(f"[CoCEO] Tool call requested: {tool_name} with args: {tool_args}")

        # For now, return a placeholder response
        # TODO: Integrate with ToolRegistry when implemented
        placeholder_result = {
            "tool": tool_name,
            "status": "placeholder",
            "message": f"Tool '{tool_name}' not yet implemented - placeholder response",
            "args": tool_args,
            "timestamp": datetime.now().isoformat()
        }

        logger.warning(f"[CoCEO] Tool '{tool_name}' not implemented yet, returning placeholder")
        return placeholder_result

    # ============================================================================
    # HELPER METHODS
    # ============================================================================

    async def _initialize_services(self):
        """Initialize planner and knowledge base if not provided."""
        if self.planner is None:
            self.planner = OrchestrationPlanner(
                llm_adapter=self.llm_adapter,
                planning_temperature=self.planning_temperature
            )
            logger.debug("[CoCEO] Initialized orchestration planner")

        if self.knowledge_base is None:
            self.knowledge_base = await initialize_knowledge_base_service()
            logger.debug("[CoCEO] Initialized knowledge base service")

    async def _enrich_plan_with_strategic_context(
        self,
        tasks: List[Task],
        user_input: str,
        context: Optional[Dict[str, Any]]
    ) -> List[Task]:
        """Add CoCEO-specific strategic context to tasks."""
        logger.debug(f"[CoCEO] Enriching plan with strategic context for {len(tasks)} tasks")

        # Use context for additional enrichment if available
        context_info = context or {}
        session_context = context_info.get("session_context", {})

        # For PoC, add simple priority scoring and strategic notes
        for i, task in enumerate(tasks):
            # Add priority based on task type and position
            priority_score = self._calculate_task_priority(task, i, len(tasks))

            # Add strategic metadata
            task.metadata.update({
                "priority_score": priority_score,
                "strategic_importance": self._assess_strategic_importance(task, user_input),
                "estimated_complexity": self._estimate_task_complexity(task),
                "enriched_by": self.agent_name,
                "enrichment_timestamp": datetime.now().isoformat(),
                "session_context_keys": list(session_context.keys()) if session_context else []
            })

        logger.debug(f"[CoCEO] Enriched {len(tasks)} tasks with strategic context")
        return tasks

    async def _determine_optimal_agent(
        self,
        task: Task,
        available_agents: List[str],
        context: Optional[LangGraphState]
    ) -> str:
        """Determine the best agent for a specific task."""
        # Rule-based approach for PoC

        # Use context for additional assignment logic if available
        context_info = {}
        if context:
            context_info = {
                "current_step": context.current_step,
                "completed_tasks": len(context.get_completed_tasks()),
                "pending_tasks": len(context.get_pending_tasks())
            }
            logger.debug(f"[CoCEO] Assignment context: {context_info}")

        # First, try rule-based mapping
        if task.task_type in self.agent_assignment_rules:
            preferred_agent = self.agent_assignment_rules[task.task_type]
            if preferred_agent in available_agents:
                logger.debug(f"[CoCEO] Assigned {task.task_id} to {preferred_agent} via rule-based mapping")
                return preferred_agent

        # Check for domain-specific keywords in task description
        task_desc_lower = task.description.lower()

        if any(keyword in task_desc_lower for keyword in ["finance", "cost", "budget", "revenue", "profit"]):
            if "Finance" in available_agents:
                logger.debug(f"[CoCEO] Assigned {task.task_id} to Finance via keyword matching")
                return "Finance"

        if any(keyword in task_desc_lower for keyword in ["marketing", "campaign", "brand", "customer", "promotion"]):
            if "Marketing" in available_agents:
                logger.debug(f"[CoCEO] Assigned {task.task_id} to Marketing via keyword matching")
                return "Marketing"

        # Default to CoCEO for complex reasoning or coordination tasks
        if "CoCEO" in available_agents:
            logger.debug(f"[CoCEO] Assigned {task.task_id} to CoCEO as default")
            return "CoCEO"

        # Fallback to first available agent
        fallback_agent = available_agents[0] if available_agents else "CoCEO"
        logger.debug(f"[CoCEO] Assigned {task.task_id} to {fallback_agent} as fallback")
        return fallback_agent

    def _calculate_execution_order(self, tasks: List[Task]) -> List[str]:
        """Calculate optimal execution order based on dependencies."""
        logger.debug(f"[CoCEO] Calculating execution order for {len(tasks)} tasks")

        # Simple topological sort based on dependencies
        ordered_tasks = []
        remaining_tasks = tasks.copy()

        while remaining_tasks:
            # Find tasks with no unresolved dependencies
            ready_tasks = [
                task for task in remaining_tasks
                if not task.dependencies or
                all(dep_id in [t.task_id for t in ordered_tasks] for dep_id in task.dependencies)
            ]

            if not ready_tasks:
                # Break circular dependencies by taking the first task
                logger.warning("[CoCEO] Circular dependency detected, breaking with first remaining task")
                ready_tasks = [remaining_tasks[0]]

            # Add ready tasks to order (sort by priority if available)
            ready_tasks.sort(key=lambda t: getattr(t, 'metadata', {}).get('priority_score', 0), reverse=True)

            for task in ready_tasks:
                ordered_tasks.append(task.task_id)
                remaining_tasks.remove(task)

        logger.debug(f"[CoCEO] Execution order calculated: {ordered_tasks}")
        return ordered_tasks

    async def _generate_coordination_notes(
        self,
        plan: List[Task],
        available_agents: List[str],
        context: Optional[LangGraphState]
    ) -> str:
        """Generate strategic notes about the coordination decisions."""
        notes = []

        # Add context information if available
        if context:
            notes.append(f"Orchestration step: {context.current_step}")
            if context.context:
                notes.append(f"Context keys: {list(context.context.keys())}")

        # Analyze task distribution
        agent_task_count = {}
        for task in plan:
            agent = task.assigned_agent or "Unassigned"
            agent_task_count[agent] = agent_task_count.get(agent, 0) + 1

        notes.append(f"Task distribution: {agent_task_count}")

        # Check agent utilization
        unused_agents = [agent for agent in available_agents if agent not in agent_task_count]
        if unused_agents:
            notes.append(f"Unused agents: {unused_agents}")

        # Identify potential bottlenecks
        max_tasks = max(agent_task_count.values()) if agent_task_count else 0
        if max_tasks > 3:
            overloaded_agents = [agent for agent, count in agent_task_count.items() if count == max_tasks]
            notes.append(f"Potential bottleneck: {overloaded_agents} with {max_tasks} tasks")

        # Check dependency complexity
        total_dependencies = sum(len(task.dependencies) for task in plan)
        if total_dependencies > len(plan):
            notes.append(f"High dependency complexity: {total_dependencies} dependencies for {len(plan)} tasks")

        return "; ".join(notes)

    def _calculate_task_priority(self, task: Task, position: int, total_tasks: int) -> float:
        """Calculate priority score for a task."""
        # Base priority on task type
        type_priority = {
            TaskType.REASONING: 0.8,
            TaskType.TOOLS: 0.6,
            TaskType.ACTION: 0.4
        }

        base_score = type_priority.get(task.task_type, 0.5)

        # Adjust based on dependencies (tasks with more dependencies get higher priority)
        dependency_bonus = len(task.dependencies) * 0.1

        # Adjust based on position (earlier tasks get slight priority boost)
        position_bonus = (total_tasks - position) / total_tasks * 0.2

        return min(1.0, base_score + dependency_bonus + position_bonus)

    def _assess_strategic_importance(self, task: Task, user_input: str) -> str:
        """Assess the strategic importance of a task."""
        # Simple keyword-based assessment
        task_desc = task.description.lower()
        user_input_lower = user_input.lower()

        high_importance_keywords = ["critical", "urgent", "important", "key", "strategic"]
        medium_importance_keywords = ["analyze", "research", "evaluate", "review"]

        if any(keyword in task_desc or keyword in user_input_lower for keyword in high_importance_keywords):
            return "high"
        elif any(keyword in task_desc for keyword in medium_importance_keywords):
            return "medium"
        else:
            return "low"

    def _estimate_task_complexity(self, task: Task) -> str:
        """Estimate the complexity of a task."""
        # Simple heuristic based on description length and dependencies
        desc_length = len(task.description)
        dependency_count = len(task.dependencies)

        if desc_length > 200 or dependency_count > 2:
            return "high"
        elif desc_length > 100 or dependency_count > 0:
            return "medium"
        else:
            return "low"

    async def _generate_fallback_plan(
        self,
        user_input: str,
        context: Optional[Dict[str, Any]]
    ) -> Tuple[List[Task], str]:
        """Generate a simple fallback plan when main planning fails."""
        logger.warning("[CoCEO] Generating fallback plan due to planning failure")

        # Use context information if available for better fallback
        context_info = context or {}
        previous_tasks = context_info.get("previous_tasks", [])

        # Create a simple single-task plan
        fallback_task = Task(
            description=f"Process user request: {user_input}",
            task_type=TaskType.REASONING,
            assigned_agent=self.agent_name
        )

        # Add metadata about fallback context
        fallback_task.metadata.update({
            "fallback_reason": "planning_failure",
            "original_context_keys": list(context_info.keys()),
            "previous_tasks_count": len(previous_tasks),
            "fallback_timestamp": datetime.now().isoformat()
        })

        analysis = f"Fallback plan generated due to planning error. Single task created for basic processing. Context: {len(previous_tasks)} previous tasks."

        return [fallback_task], analysis

    def _build_decision_messages(
        self,
        decision_context: Dict[str, Any],
        options: List[Dict[str, Any]],
        strategic_context: Dict[str, Any],
        context: Optional[LangGraphState]
    ) -> List[ChatMessage]:
        """Build messages for strategic decision making."""

        # System message defining CoCEO role
        system_message = {
            "role": "system",
            "content": f"""You are the CoCEO agent in a multi-agent orchestration system.
Your role is to make strategic decisions based on available information and context.

Your capabilities: {', '.join(self.capabilities)}
Available agents: {', '.join(self.available_agents)}

You must analyze the given options and make a decision based on:
1. Strategic context from RAG and memory
2. Current orchestration state
3. Business logic and efficiency
4. Risk assessment

Respond with a JSON object containing:
- "choice": the selected option (index or identifier)
- "reasoning": detailed explanation of your decision
- "confidence": confidence score (0.0 to 1.0)
- "alternatives": brief notes on other considered options
"""
        }

        # User message with decision context
        context_summary = f"""
Decision Context: {decision_context.get('description', 'No description provided')}

Available Options:
{json.dumps(options, indent=2)}

Strategic Context:
- RAG Results: {len(strategic_context.get('rag_results', []))} documents found
- Memory Context: {len(strategic_context.get('memory_context', []))} relevant memories
- Current Step: {context.current_step if context else 'Unknown'}

Please make your strategic decision and provide detailed reasoning.
"""

        user_message = {
            "role": "user",
            "content": context_summary
        }

        return [system_message, user_message]

    async def _parse_strategic_decision(
        self,
        decision_response: str,
        options: List[Dict[str, Any]],
        decision_context: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Parse LLM response into structured decision result."""
        try:
            # Try to parse JSON response
            if "{" in decision_response and "}" in decision_response:
                # Extract JSON from response
                start_idx = decision_response.find("{")
                end_idx = decision_response.rfind("}") + 1
                json_str = decision_response[start_idx:end_idx]

                decision_data = json.loads(json_str)

                # Validate required fields
                if "choice" in decision_data and "reasoning" in decision_data:
                    result = {
                        "choice": decision_data.get("choice"),
                        "reasoning": decision_data.get("reasoning"),
                        "confidence": decision_data.get("confidence", 0.7),
                        "alternatives": decision_data.get("alternatives", ""),
                        "raw_response": decision_response,
                        "decision_context": decision_context.get("description", "No context")
                    }
                    return result

            # Fallback parsing if JSON parsing fails
            logger.warning("[CoCEO] Could not parse JSON from decision response, using fallback")
            return {
                "choice": options[0] if options else {},
                "reasoning": f"Fallback decision based on response: {decision_response[:200]}...",
                "confidence": 0.5,
                "alternatives": "Could not parse structured alternatives",
                "raw_response": decision_response,
                "decision_context": decision_context.get("description", "No context")
            }

        except Exception as e:
            logger.error(f"[CoCEO] Error parsing strategic decision: {e}")
            return {
                "choice": options[0] if options else {},
                "reasoning": f"Error parsing decision: {e}",
                "confidence": 0.1,
                "alternatives": "Error in parsing",
                "raw_response": decision_response,
                "decision_context": decision_context.get("description", "No context")
            }

    async def _store_strategic_decision(
        self,
        decision_result: Dict[str, Any],
        context: Optional[LangGraphState]
    ):
        """Store strategic decision in memory for future reference."""
        if self.memory:
            decision_memory = MemoryUnits(
                content=f"Strategic decision: {decision_result['choice']} | Reasoning: {decision_result['reasoning'][:200]}...",
                agent=self.agent_name,
                task_id="strategic_decision",
                current_step=context.current_step if context else OrchestrationStep.PLANNING,
                tags=["strategic", "decision", "coordination"],
                metadata={
                    "decision_choice": str(decision_result['choice']),
                    "confidence": decision_result['confidence'],
                    "full_reasoning": decision_result['reasoning']
                }
            )
            self.memory.add_memory(decision_memory)
            logger.debug("[CoCEO] Stored strategic decision in memory")
