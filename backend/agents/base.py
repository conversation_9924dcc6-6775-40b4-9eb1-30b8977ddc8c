"""
BaseAgent class for multi-agent orchestration system.

This class provides a reusable base structure for all agents involved in task execution.
The class handles:
-> Running assigned tasks (async def run(task, context))
    -> 1. Type safety checks
    -> 2. Extracting key task information
    -> 3. Retrieving relevant memory context (RAG)
    -> 4. Building LLM messages
    -> 5. Interacting with the LLM to get response
    -> 6. Storing result in memory (RAG)
    -> 7. Injecting outputs into the LangGraph state
"""

from ..llm.base import LLMAdapter
# from ..tools.base import BaseTool
from ..orchestration.memory import GlobalMemory, MemoryUnits
from ..orchestration.state import LangGraphState, Task

import logging
logger = logging.getLogger(__name__)

"""
Base class for all agents in the orchestration system.
    
    Args:
        llm_adapter (LLMAdapter): Adapter for calling the underlying LLM (e.g., OpenAI, Anthropic).
        memory (GlobalMemory, optional): LangGraph's shared memory object (`memory.py`) for context injection and traceability.
"""
class BaseAgent:
    def __init__(
        self,
        llm_adapter: LLMAdapter,
        # tools: List[BaseTool] = None, <- requires Tool implementation
        memory: GlobalMemory = None,
    ):

        self.llm_adapter = llm_adapter
        # self.tools = tools or [] <- requires Tool implementation
        self.memory = memory


    """
    Run a task (async) using the agent's reasoning logic and optional memory/context retrieval.

    This method is marked as `async` because it awaits potentially time-consuming operations:
    - Calling the LLM via `llm_adapter.chat()`, which performs a network request.
    - In the future, it may await database lookups, web search tools, or other asynchronous services.

    Using `async def` enables non-blocking execution within LangGraph or any async orchestration loop.

    Args:
        task (Task): The task object containing metadata, type, and description.
        context (LangGraphState): The orchestration state, including step, context, and outputs.

    Returns:
        str: The LLM-generated output that completes the task.
    """
    async def run(self, task: Task, context: LangGraphState) -> str:
        # --- 1. Type safety checks ---
        if not isinstance(task, Task):
            raise TypeError("Expected a `Task` object")
        if not isinstance(context, LangGraphState):
            raise TypeError("Expected a `LangGraphState` object")

        # --- 2. Extract key task information ---
        task_id = task.task_id
        task_type = task.task_type
        task_description = task.description
        assigned_agent = task.assigned_agent or "unknown"

        logger.info("Running task %s (%s) for agent %s", task_id, task_type, assigned_agent)
        logger.debug("Task description: %s", task_description)
        logger.debug("Current LangGraph step: %s", context.current_step)

        # --- 3. Retrieve relevant memory context ---
        memory_snippets = []
        if self.memory:
            filters = {"task_id": task_id, "agent": assigned_agent}
            memory_snippets = self.memory.get_relevant_memory(filters)

        # Format snippets into a string
        memory_context = (
            "\n".join([m.content for m in memory_snippets]) if memory_snippets else ""
        )

        # --- 4. Build LLM messages ---
        messages = [
            {
                "role": "system",
                "content": f"You are the agent '{assigned_agent}' working on the following task: {task_description}",
            },
            {
                "role": "user",
                "content": (
                    f"Context from memory:\n{memory_context}"
                    if memory_context
                    else "No prior memory."
                ),
            },
        ]

        # --- 5. Call LLM to get response ---
        try:
            response = await self.llm_adapter.chat(messages=messages)
        except Exception as e:
            logger.error("LLM call failed for task %s: %s", task_id, e)
            # Store error in memory if available
            if self.memory:
                error_memory = MemoryUnits(
                    content=f"LLM call failed: {str(e)}",
                    agent=assigned_agent,
                    task_id=task_id,
                    current_step=context.current_step,
                    tags=["error", "llm_failure"],
                )
                self.memory.add_memory(error_memory)
            raise

        # --- 6. Store result in memory ---
        if self.memory:
            memory_unit = MemoryUnits(
                content=response,
                agent=assigned_agent,
                task_id=task_id,
                current_step=context.current_step,
                tags=["llm_output"],
            )
            self.memory.add_memory(memory_unit)

        # --- 7. Inject output into LangGraph state ---
        context.agent_outputs[assigned_agent] = response

        return response
